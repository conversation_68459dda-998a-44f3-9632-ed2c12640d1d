<?php

namespace App\Services;

use App\Models\ProductCategory;
use Illuminate\Support\Facades\Log;

class CategoryImportService
{
    protected $teamId;
    protected $categoryCache = [];
    protected $errors = [];

    public function __construct($teamId)
    {
        $this->teamId = $teamId;
        $this->loadCategoryCache();
    }

    /**
     * Load all categories for the team into cache for faster lookups
     */
    protected function loadCategoryCache()
    {
        $categories = ProductCategory::where('team_id', $this->teamId)->get();
        
        foreach ($categories as $category) {
            if ($category->slug) {
                $this->categoryCache[$category->slug] = $category;
            }
        }
    }

    /**
     * Convert category slugs to IDs and validate relationships
     */
    public function convertSlugsToIds($rowData)
    {
        $result = $rowData;
        $errors = [];

        // Handle field mapping for products
        if (isset($rowData['description']) && !isset($rowData['name'])) {
            $result['name'] = $rowData['description'];
        }

        // Convert main category
        if (!empty($rowData['product_category_id'])) {
            $mainCategory = $this->findCategoryBySlug($rowData['product_category_id'], 1);
            if ($mainCategory) {
                $result['product_category_id'] = $mainCategory->id;
            } else {
                $errors[] = "Main category '{$rowData['product_category_id']}' not found";
                $result['product_category_id'] = null;
            }
        }

        // Convert sub category
        if (!empty($rowData['product_sub_category_id'])) {
            $subCategory = $this->findCategoryBySlug($rowData['product_sub_category_id'], 2);
            if ($subCategory) {
                // Validate that sub category belongs to main category
                if (!empty($result['product_category_id']) && $subCategory->parent_id != $result['product_category_id']) {
                    $errors[] = "Sub category '{$rowData['product_sub_category_id']}' does not belong to main category";
                    $result['product_sub_category_id'] = null;
                } else {
                    $result['product_sub_category_id'] = $subCategory->id;
                }
            } else {
                $errors[] = "Sub category '{$rowData['product_sub_category_id']}' not found";
                $result['product_sub_category_id'] = null;
            }
        }

        // Convert sub sub category
        if (!empty($rowData['product_sub_sub_category_id'])) {
            $subSubCategory = $this->findCategoryBySlug($rowData['product_sub_sub_category_id'], 3);
            if ($subSubCategory) {
                // Validate that sub sub category belongs to sub category
                if (!empty($result['product_sub_category_id']) && $subSubCategory->parent_id != $result['product_sub_category_id']) {
                    $errors[] = "Sub sub category '{$rowData['product_sub_sub_category_id']}' does not belong to sub category";
                    $result['product_sub_sub_category_id'] = null;
                } else {
                    $result['product_sub_sub_category_id'] = $subSubCategory->id;
                }
            } else {
                $errors[] = "Sub sub category '{$rowData['product_sub_sub_category_id']}' not found";
                $result['product_sub_sub_category_id'] = null;
            }
        }

        return [
            'data' => $result,
            'errors' => $errors
        ];
    }

    /**
     * Find category by slug and level
     */
    protected function findCategoryBySlug($slug, $level)
    {
        if (isset($this->categoryCache[$slug])) {
            $category = $this->categoryCache[$slug];
            if ($category->level == $level) {
                return $category;
            }
        }
        return null;
    }

    /**
     * Get all accumulated errors
     */
    public function getErrors()
    {
        return $this->errors;
    }

    /**
     * Add error to the collection
     */
    public function addError($error)
    {
        $this->errors[] = $error;
    }

    /**
     * Clear errors
     */
    public function clearErrors()
    {
        $this->errors = [];
    }

    /**
     * Validate category hierarchy for a complete row
     */
    public function validateCategoryHierarchy($mainCategoryId, $subCategoryId = null, $subSubCategoryId = null)
    {
        $errors = [];

        // If sub category is provided, validate it belongs to main category
        if ($subCategoryId && $mainCategoryId) {
            $subCategory = ProductCategory::find($subCategoryId);
            if (!$subCategory || $subCategory->parent_id != $mainCategoryId) {
                $errors[] = "Sub category does not belong to the specified main category";
            }
        }

        // If sub sub category is provided, validate it belongs to sub category
        if ($subSubCategoryId && $subCategoryId) {
            $subSubCategory = ProductCategory::find($subSubCategoryId);
            if (!$subSubCategory || $subSubCategory->parent_id != $subCategoryId) {
                $errors[] = "Sub sub category does not belong to the specified sub category";
            }
        }

        return $errors;
    }
}
