<?php

namespace App\Services;

use App\Models\ProductCategory;
use Illuminate\Support\Facades\Log;

class CategoryImportService
{
    protected $teamId;
    protected $categoryCache = [];
    protected $errors = [];
    protected $createdCategories = [];

    public function __construct($teamId)
    {
        $this->teamId = $teamId;
        $this->loadCategoryCache();
    }

    /**
     * Load all categories for the team into cache for faster lookups
     */
    protected function loadCategoryCache()
    {
        $categories = ProductCategory::where('team_id', $this->teamId)->get();
        
        foreach ($categories as $category) {
            if ($category->slug) {
                $this->categoryCache[$category->slug] = $category;
            }
        }
    }

    /**
     * Convert category slugs to IDs and validate/create relationships
     */
    public function convertSlugsToIds($rowData)
    {
        $result = $rowData;
        $errors = [];

        // Handle field mapping for products
        if (isset($rowData['description']) && !isset($rowData['name'])) {
            $result['name'] = $rowData['description'];
        }

        // Process categories in hierarchical order
        $mainCategoryId = null;
        $subCategoryId = null;
        $subSubCategoryId = null;

        // Convert/Create main category
        if (!empty($rowData['product_category_id'])) {
            $mainCategory = $this->findOrCreateCategory($rowData['product_category_id'], 1);
            if ($mainCategory) {
                $mainCategoryId = $mainCategory->id;
                $result['product_category_id'] = $mainCategoryId;
            } else {
                $errors[] = "Failed to create main category '{$rowData['product_category_id']}'";
            }
        }

        // Convert/Create sub category
        if (!empty($rowData['product_sub_category_id']) && $mainCategoryId) {
            $subCategory = $this->findOrCreateCategory($rowData['product_sub_category_id'], 2, $mainCategoryId);
            if ($subCategory) {
                $subCategoryId = $subCategory->id;
                $result['product_sub_category_id'] = $subCategoryId;
            } else {
                $errors[] = "Failed to create sub category '{$rowData['product_sub_category_id']}'";
            }
        } elseif (!empty($rowData['product_sub_category_id']) && !$mainCategoryId) {
            $errors[] = "Cannot create sub category '{$rowData['product_sub_category_id']}' without main category";
            $result['product_sub_category_id'] = null;
        }

        // Convert/Create sub sub category
        if (!empty($rowData['product_sub_sub_category_id']) && $subCategoryId) {
            $subSubCategory = $this->findOrCreateCategory($rowData['product_sub_sub_category_id'], 3, $subCategoryId);
            if ($subSubCategory) {
                $subSubCategoryId = $subSubCategory->id;
                $result['product_sub_sub_category_id'] = $subSubCategoryId;
            } else {
                $errors[] = "Failed to create sub sub category '{$rowData['product_sub_sub_category_id']}'";
            }
        } elseif (!empty($rowData['product_sub_sub_category_id']) && !$subCategoryId) {
            $errors[] = "Cannot create sub sub category '{$rowData['product_sub_sub_category_id']}' without sub category";
            $result['product_sub_sub_category_id'] = null;
        }

        return [
            'data' => $result,
            'errors' => $errors
        ];
    }

    /**
     * Find or create category by slug and level
     */
    protected function findOrCreateCategory($slug, $level, $parentId = null)
    {
        // First try to find existing category
        $category = $this->findCategoryBySlug($slug, $level);

        if ($category) {
            // If found, validate parent relationship for sub categories
            if ($level > 1 && $parentId && $category->parent_id != $parentId) {
                // Category exists but with wrong parent, create a new one with unique slug
                return $this->createCategory($slug, $level, $parentId);
            }
            return $category;
        }

        // Category not found, create it
        return $this->createCategory($slug, $level, $parentId);
    }

    /**
     * Create a new category
     */
    protected function createCategory($slug, $level, $parentId = null)
    {
        try {
            // Generate a human-readable title from slug
            $title = $this->slugToTitle($slug);

            // Ensure unique slug for this team
            $uniqueSlug = ProductCategory::generateUniqueSlug($slug, $this->teamId);

            $categoryData = [
                'title' => $title,
                'slug' => $uniqueSlug,
                'status' => 'active',
                'team_id' => $this->teamId,
                'level' => $level,
            ];

            if ($parentId) {
                $categoryData['parent_id'] = $parentId;
            }

            $category = ProductCategory::create($categoryData);

            // Add to cache for future lookups
            $this->categoryCache[$slug] = $category;

            // Track created categories
            $this->createdCategories[] = [
                'title' => $title,
                'level' => $level,
                'slug' => $uniqueSlug
            ];

            Log::info("Created new category", [
                'title' => $title,
                'slug' => $uniqueSlug,
                'level' => $level,
                'parent_id' => $parentId,
                'team_id' => $this->teamId
            ]);

            return $category;

        } catch (\Exception $e) {
            Log::error("Failed to create category", [
                'slug' => $slug,
                'level' => $level,
                'parent_id' => $parentId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Convert slug to human-readable title
     */
    protected function slugToTitle($slug)
    {
        // Replace underscores and hyphens with spaces
        $title = str_replace(['_', '-'], ' ', $slug);

        // Capitalize each word
        $title = ucwords($title);

        return $title;
    }

    /**
     * Find category by slug and level
     */
    protected function findCategoryBySlug($slug, $level)
    {
        if (isset($this->categoryCache[$slug])) {
            $category = $this->categoryCache[$slug];
            if ($category->level == $level) {
                return $category;
            }
        }
        return null;
    }

    /**
     * Get all accumulated errors
     */
    public function getErrors()
    {
        return $this->errors;
    }

    /**
     * Add error to the collection
     */
    public function addError($error)
    {
        $this->errors[] = $error;
    }

    /**
     * Clear errors
     */
    public function clearErrors()
    {
        $this->errors = [];
    }

    /**
     * Get created categories
     */
    public function getCreatedCategories()
    {
        return $this->createdCategories;
    }

    /**
     * Get created categories count
     */
    public function getCreatedCategoriesCount()
    {
        return count($this->createdCategories);
    }

    /**
     * Validate category hierarchy for a complete row
     */
    public function validateCategoryHierarchy($mainCategoryId, $subCategoryId = null, $subSubCategoryId = null)
    {
        $errors = [];

        // If sub category is provided, validate it belongs to main category
        if ($subCategoryId && $mainCategoryId) {
            $subCategory = ProductCategory::find($subCategoryId);
            if (!$subCategory || $subCategory->parent_id != $mainCategoryId) {
                $errors[] = "Sub category does not belong to the specified main category";
            }
        }

        // If sub sub category is provided, validate it belongs to sub category
        if ($subSubCategoryId && $subCategoryId) {
            $subSubCategory = ProductCategory::find($subSubCategoryId);
            if (!$subSubCategory || $subSubCategory->parent_id != $subCategoryId) {
                $errors[] = "Sub sub category does not belong to the specified sub category";
            }
        }

        return $errors;
    }
}
