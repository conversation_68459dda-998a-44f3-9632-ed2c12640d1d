<?php

namespace App\Models;

use App\Traits\Auditable;
use DateTimeInterface;
use App\Traits\MultiTenantModelTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductCategory extends Model
{
    use SoftDeletes, MultiTenantModelTrait, Auditable, HasFactory;

    public $table = 'product_categories';

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public const STATUS_SELECT = [
        'active'   => 'Active',
        'inactive' => 'Inactive',
    ];

    protected $fillable = [
        'title',
        'slug',
        'status',
        'created_at',
        'updated_at',
        'deleted_at',
        'team_id',
        'parent_id',
        'level',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }
    
    public function team()
    {
        return $this->belongsTo(Team::class, 'team_id');
    }

    // Hierarchical relationships
    public function parent()
    {
        return $this->belongsTo(ProductCategory::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(ProductCategory::class, 'parent_id');
    }

    // Helper methods
    public function isMainCategory()
    {
        return $this->level === 1 || is_null($this->parent_id);
    }

    public function isSubCategory()
    {
        return $this->level === 2;
    }

    public function isSubSubCategory()
    {
        return $this->level === 3;
    }

    // Get all main categories
    public static function mainCategories()
    {
        return self::where('level', 1)->orWhereNull('parent_id')->get();
    }

    // Get subcategories of a category
    public function subcategories()
    {
        return $this->children()->where('level', $this->level + 1);
    }

    // Get the full path of the category (Main > Sub > SubSub)
    public function getFullPathAttribute()
    {
        $path = $this->title;

        if ($this->parent) {
            $path = $this->parent->title . ' > ' . $path;

            if ($this->parent->parent) {
                $path = $this->parent->parent->title . ' > ' . $path;
            }
        }

        return $path;
    }

    // Find category by slug within team
    public static function findBySlug($slug, $teamId)
    {
        return self::where('slug', $slug)
                   ->where('team_id', $teamId)
                   ->first();
    }

    // Generate unique slug for team
    public static function generateUniqueSlug($baseSlug, $teamId, $excludeId = null)
    {
        $slug = $baseSlug;
        $counter = 1;

        while (self::slugExistsInTeam($slug, $teamId, $excludeId)) {
            $slug = $baseSlug . '_' . $counter;
            $counter++;
        }

        return $slug;
    }

    // Check if slug exists in team
    public static function slugExistsInTeam($slug, $teamId, $excludeId = null)
    {
        $query = self::where('slug', $slug)->where('team_id', $teamId);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }
}
