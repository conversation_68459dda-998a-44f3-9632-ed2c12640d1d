<?php

namespace App\Http\Controllers\Traits;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use SpreadsheetReader;
use App\Services\CategoryImportService;
use Illuminate\Support\Facades\Log;

trait CsvImportTrait
{
    public function processCsvImport(Request $request)
    {
        try {
            $filename = $request->input('filename', false);
            $path     = storage_path('app/csv_import/' . $filename);

            $hasHeader = $request->input('hasHeader', false);

            $fields = $request->input('fields', false);
            $fields = array_flip(array_filter($fields));

            $modelName = $request->input('modelName', false);
            $model     = "App\Models\\" . $modelName;

            $reader = new SpreadsheetReader($path);
            $insert = [];
            $errors = [];
            $skippedRows = 0;

            // Initialize category service for Product imports
            $categoryService = null;
            if ($modelName === 'Product' && auth()->user() && auth()->user()->team_id) {
                $categoryService = new CategoryImportService(auth()->user()->team_id);
            }

            foreach ($reader as $key => $row) {
                if ($hasHeader && $key == 0) {
                    continue;
                }

                $tmp = [];
                foreach ($fields as $header => $k) {
                    if (isset($row[$k])) {
                        $tmp[$header] = $row[$k];
                    }
                }

                if (count($tmp) > 0) {
                    // Add team_id for models that require it
                    if (auth()->user() && auth()->user()->team_id) {
                        $tmp['team_id'] = auth()->user()->team_id;
                    }

                    // Handle Product-specific category conversion
                    if ($modelName === 'Product' && $categoryService) {
                        $conversionResult = $categoryService->convertSlugsToIds($tmp);

                        if (!empty($conversionResult['errors'])) {
                            $errors[] = "Row " . ($key + 1) . ": " . implode(', ', $conversionResult['errors']);
                            $skippedRows++;
                            continue; // Skip this row if there are category errors
                        }

                        $tmp = $conversionResult['data'];
                    }

                    // Add timestamps
                    $tmp['created_at'] = now();
                    $tmp['updated_at'] = now();

                    $insert[] = $tmp;
                }
            }

            $for_insert = array_chunk($insert, 100);

            foreach ($for_insert as $insert_item) {
                $model::insert($insert_item);
            }

            $rows  = count($insert);
            $table = Str::plural($modelName);

            File::delete($path);

            // Prepare success message with error details if any
            $message = trans('global.app_imported_rows_to_table', ['rows' => $rows, 'table' => $table]);

            if ($skippedRows > 0) {
                $message .= " ($skippedRows rows skipped due to errors)";
            }

            // Add information about created categories
            if ($modelName === 'Product' && $categoryService) {
                $createdCategories = $categoryService->getCreatedCategories();
                if (!empty($createdCategories)) {
                    $categoryNames = array_map(function($cat) {
                        return $cat['title'];
                    }, $createdCategories);
                    $message .= " New categories created: " . implode(', ', $categoryNames);
                }
            }

            if (!empty($errors)) {
                Log::warning('CSV Import Errors', ['errors' => $errors, 'model' => $modelName]);
                session()->flash('import_errors', $errors);
            }

            session()->flash('message', $message);

            return redirect($request->input('redirect'));
        } catch (\Exception $ex) {
            Log::error('CSV Import Exception', ['error' => $ex->getMessage(), 'trace' => $ex->getTraceAsString()]);
            throw $ex;
        }
    }

    public function parseCsvImport(Request $request)
    {
        $file = $request->file('csv_file');
        $request->validate([
            'csv_file' => 'mimes:csv,txt',
        ]);

        $path      = $file->path();
        $hasHeader = $request->input('header', false) ? true : false;

        $reader  = new SpreadsheetReader($path);
        $headers = $reader->current();
        $lines   = [];

        $i = 0;
        while ($reader->next() !== false && $i < 500) {
            $lines[] = $reader->current();
            $i++;
        }

        $filename = Str::random(10) . '.csv';
        $file->storeAs('csv_import', $filename);

        $modelName     = $request->input('model', false);
        $fullModelName = "App\Models\\" . $modelName;

        $model     = new $fullModelName();
        $fillables = $model->getFillable();

        $redirect = url()->previous();

        $routeName = 'admin.' . strtolower(Str::plural(Str::kebab($modelName))) . '.processCsvImport';

        return view('csvImport.parseInput', compact('headers', 'filename', 'fillables', 'hasHeader', 'modelName', 'lines', 'redirect', 'routeName'));
    }
}
