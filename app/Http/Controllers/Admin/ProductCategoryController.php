<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\CsvImportTrait;
use App\Http\Requests\MassDestroyProductCategoryRequest;
use App\Http\Requests\StoreProductCategoryRequest;
use App\Http\Requests\UpdateProductCategoryRequest;
use App\Models\ProductCategory;
use Gate;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Yajra\DataTables\Facades\DataTables;

class ProductCategoryController extends Controller
{
    use CsvImportTrait;

    public function index(Request $request)
    {
        abort_if(Gate::denies('product_category_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        if ($request->ajax()) {
            $query = ProductCategory::query()->select(sprintf('%s.*', (new ProductCategory)->table));
            $table = Datatables::of($query);

            $table->addColumn('placeholder', '&nbsp;');
            $table->addColumn('actions', '&nbsp;');

            $table->editColumn('actions', function ($row) {
                $viewGate      = 'product_category_show';
                $editGate      = 'product_category_edit';
                $deleteGate    = 'product_category_delete';
                $crudRoutePart = 'product-categories';

                return view('partials.datatablesActions', compact(
                    'viewGate',
                    'editGate',
                    'deleteGate',
                    'crudRoutePart',
                    'row'
                ));
            });

            $table->editColumn('id', function ($row) {
                return $row->id ? $row->id : '';
            });
            $table->editColumn('title', function ($row) {
                return $row->title ? '<a href="' . route('admin.product-categories.show', $row->id) . '" class="badge bg-primary text-white">' . $row->title . '</a>' : '';
            });
            $table->editColumn('status', function ($row) {
                return $row->status ? ProductCategory::STATUS_SELECT[$row->status] : '';
            });
            $table->editColumn('level', function ($row) {
                $levelLabels = [
                    1 => '<span class="badge bg-primary">Main Category</span>',
                    2 => '<span class="badge bg-info">Sub Category</span>',
                    3 => '<span class="badge bg-warning">Sub-Sub Category</span>',
                ];
                return isset($levelLabels[$row->level]) ? $levelLabels[$row->level] : '';
            });
            $table->editColumn('parent', function ($row) {
                return $row->parent ? $row->parent->title : '';
            });

            $table->rawColumns(['actions', 'placeholder', 'title', 'level']);

            return $table->make(true);
        }

        $mainCategories = ProductCategory::mainCategories();
        
        return view('admin.productCategories.index', compact('mainCategories'));
    }

    public function create()
    {
        abort_if(Gate::denies('product_category_create'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $parentCategories = ProductCategory::where(function($query) {
            $query->where('level', '<', 3)->orWhereNull('level');
        })->pluck('title', 'id')->prepend(trans('global.pleaseSelect'), '');

        return view('admin.productCategories.create', compact('parentCategories'));
    }

    public function store(StoreProductCategoryRequest $request)
    {
        $data = $request->all();
        
        // Set level based on parent
        if (!empty($data['parent_id'])) {
            $parent = ProductCategory::find($data['parent_id']);
            $data['level'] = $parent ? $parent->level + 1 : 1;
        } else {
            $data['level'] = 1;
        }

        if ($request->ajax()) {
            $productCategory = ProductCategory::create($data);

            return response()->json([
                'success' => 'Status updated successfully.',
                'product_categories' => $productCategory
            ]);
        }

        $productCategory = ProductCategory::create($data);

        return redirect()->route('admin.product-categories.index');
    }

    public function edit(ProductCategory $productCategory)
    {
        abort_if(Gate::denies('product_category_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        // Get potential parent categories (exclude itself and its descendants to prevent circular references)
        $parentCategories = ProductCategory::where('id', '!=', $productCategory->id)
            ->where(function($query) use ($productCategory) {
                // For level 1 (main) categories, can only select null parent
                if ($productCategory->level === 1) {
                    $query->whereNull('parent_id');
                }
                // For level 2 (sub) categories, can only select level 1 parents
                elseif ($productCategory->level === 2) {
                    $query->where('level', 1)->orWhereNull('level');
                }
                // For level 3 (sub-sub) categories, can only select level 2 parents
                elseif ($productCategory->level === 3) {
                    $query->where('level', 2);
                }
            })
            ->pluck('title', 'id')
            ->prepend(trans('global.pleaseSelect'), '');

        return view('admin.productCategories.edit', compact('productCategory', 'parentCategories'));
    }

    public function update(UpdateProductCategoryRequest $request, ProductCategory $productCategory)
    {
        $data = $request->all();
        
        // Set level based on parent
        if (!empty($data['parent_id'])) {
            $parent = ProductCategory::find($data['parent_id']);
            $data['level'] = $parent ? $parent->level + 1 : 1;
        } else {
            $data['level'] = 1;
        }
        
        $productCategory->update($data);

        return redirect()->route('admin.product-categories.index');
    }

    public function show(ProductCategory $productCategory)
    {
        abort_if(Gate::denies('product_category_show'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        // Load relationships
        $productCategory->load('parent');
        $subcategories = $productCategory->children()->orderBy('title')->get();

        return view('admin.productCategories.show', compact('productCategory', 'subcategories'));
    }

    public function destroy(ProductCategory $productCategory)
    {
        abort_if(Gate::denies('product_category_delete'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $productCategory->delete();

        return back();
    }

    public function massDestroy(MassDestroyProductCategoryRequest $request)
    {
        $productCategories = ProductCategory::find(request('ids'));

        foreach ($productCategories as $productCategory) {
            $productCategory->delete();
        }

        return response(null, Response::HTTP_NO_CONTENT);
    }

    /**
     * Get subcategories for a given parent category via AJAX
     */
    public function getSubcategories(Request $request)
    {
        $parentId = $request->parent_id;
        $subcategories = ProductCategory::where('parent_id', $parentId)
            ->where('level', 2)
            ->where('status', 'active')
            ->orderBy('title')
            ->get(['id', 'title']);
        
        return response()->json($subcategories);
    }

    /**
     * Get sub-subcategories for a given parent subcategory via AJAX
     */
    public function getSubSubcategories(Request $request)
    {
        $parentId = $request->parent_id;
        $subSubcategories = ProductCategory::where('parent_id', $parentId)
            ->where('level', 3)
            ->where('status', 'active')
            ->orderBy('title')
            ->get(['id', 'title']);
        
        return response()->json($subSubcategories);
    }
}
