<?php

namespace App\Http\Requests;

use App\Models\Product;
use Gate;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Response;

class StoreProductRequest extends FormRequest
{
    public function authorize()
    {
        return Gate::allows('product_create');
    }

    public function rules()
    {
        return [
            'product_code' => [
                'string',
                'nullable',
            ],
            'name' => [
                'string',
                'nullable',
            ],
            'product_category_id' => [
                'integer',
                'nullable',
                'exists:product_categories,id',
            ],
            'product_sub_category_id' => [
                'integer',
                'nullable',
                'exists:product_categories,id',
            ],
            'product_sub_sub_category_id' => [
                'integer',
                'nullable',
                'exists:product_categories,id',
            ],
            'product_tags.*' => [
                'integer',
            ],
            'product_tags' => [
                'array',
            ],
            'min_restock_level' => [
                'string',
                'nullable',
            ],
            'unit_sales_price' => [
                'numeric',
            ],
            'unit_purchase_price' => [
                'numeric',
            ],
            'opening_stock_level' => [
                'string',
                'nullable',
            ],
            'available_stock_level' => [
                'string',
                'nullable',
            ],
        ];
    }
}
